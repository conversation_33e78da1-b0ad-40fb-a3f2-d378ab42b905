{{ 'comparison-table.css' | asset_url | stylesheet_tag }}
{% liquid
  assign sorted_tech = shop.metaobjects[section.settings.metaobject_type].values | sort_natural: 'custom_sort'
  assign feature_keys = section.settings.feature_keys | split: ','
  assign feature_labels = section.settings.feature_labels | split: ','
  assign show_rows = section.settings.show_rows
%}
{%- style -%}
  .section-{{ section.id }}-margin {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
    margin-bottom: {{ section.settings.margin_bottom | times: 0.75 | round: 0 }}px;
  }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-margin {
      margin-top: {{ section.settings.margin_top }}px;
      margin-bottom: {{ section.settings.margin_bottom }}px;
    }
  }
  .comparison-table tbody tr:last-child td:nth-child(2),
  .comparison-table tbody tr:nth-child({{ show_rows }}) td:nth-child(2) {
    border-radius: 0 0 8px 8px;
    border-bottom: 2px solid #ec3c4a;
  }
{%- endstyle -%}
<div class="compare-table-section {{ section.settings.desktop_section_padding_top_bottom }} {{ section.settings.mobile_section_padding_top_bottom }} relative overflow-hidden section-{{ section.id }}-margin py-12 md:py-16 lg:py-20">
  {% if section.settings.gradient_bg %}
    <div class="float-bg absolute top-0"></div>
  {% endif %}
  <div class="container px-4 lg:px-0">
    <div class="text-block w-full text-center mx-auto mb-8 md:mb-12">
      <h1 class="section-heading-40 text-secondary font-bold">
        {{- section.settings.title -}}
      </h1>
      {% unless section.settings.paragraph == blank %}
        <div class="mt-2 text-base">{{- section.settings.paragraph -}}</div>
      {% endunless %}
    </div>
    <div class="comparison-table-wrapper rounded-2xl overflow-x-auto border-2 border-gray-2 hidden md:block">
      <table class="comparison-table table-auto w-full text-left whitespace-no-wrap border-separate border-spacing-0">
        <thead>
          <tr>
            <th class="min-w-48 w-48 px-2 py-4 font-bold"></th>
            {% for tech in sorted_tech %}
              <th
                scope="col"
                role="columnheader"
                class="max-w-48 w-[187px] px-2 py-4 font-bold"
              >
                {{- tech.service -}}
              </th>
            {% endfor %}
          </tr>
        </thead>
        <tbody data-show-rows="{{ show_rows }}">
          {% for feature_key in feature_keys %}
            <tr class="{% if forloop.index > show_rows %}toggle-rows hidden{% endif %}">
              <td>
                {{- feature_labels[forloop.index0] -}}
              </td>
              {% for tech in sorted_tech %}
                <td>{% render 'tech-feature-value', feature_value: tech[feature_key], class: 'justify-center' %}</td>
              {% endfor %}
            </tr>
          {% endfor %}
        </tbody>
      </table>
      {% render 'see-more-toggle' %}
    </div>
    <div class="comparison-cards md:hidden">
      <div class="comparison-cards-inner-wrapper w-full flex overflow-x-auto gap-4 snap-x snap-mandatory">
        {% for tech in sorted_tech %}
          <div class="comparison-card-item border rounded-lg shadow-sm bg-white border-gray-2 snap-always snap-start max-w-[85%] h-fit min-w-[85%]">
            <div class="card-header w-full flex justify-start items-center p-6">
              <h2 class="text-3xl font-bold">
                {{- tech.service -}}
              </h2>
            </div>
            <div class="compare-cards">
              {% for feature_key in feature_keys %}
                <div class="compare-card grid grid-cols-2 gap-2 justify-between items-center {% if forloop.index > show_rows %}toggle-rows hidden{% endif %}">
                  <span class="feature-heading text-base text-secondary">
                    {{- feature_labels[forloop.index0] -}}
                  </span>
                  <span class="feature-value text-right text-base text-secondary">
                    {% render 'tech-feature-value', feature_value: tech[feature_key], class: 'justify-end' %}
                  </span>
                </div>
              {% endfor %}
            </div>
          </div>
        {% endfor %}
      </div>
      {% render 'see-more-toggle' %}
    </div>
    {% unless section.settings.message == blank %}
      <p class="text-sm mt-5 md:mt-6 text-gray-8 text-center">{{- section.settings.message -}}</p>
    {% endunless %}
  </div>
</div>
{% schema %}
{
  "name": "Comparison Table",
  "settings": [
    {
      "type": "header",
      "content": "Section Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Styku vs Other Technologies"
    },
    {
      "type": "text",
      "id": "paragraph",
      "label": "Section Description",
      "default": "Setting the new standard in health assessment technology."
    },
    {
      "type": "text",
      "id": "message",
      "label": "Add the message to show at the bottom of the table"
    },
    {
      "type": "checkbox",
      "default": true,
      "id": "gradient_bg",
      "label": "Enable and disable the Background color"
    },
    {
      "type": "header",
      "content": "Table Settings"
    },
    {
      "type": "range",
      "id": "show_rows",
      "label": "Number of Table Rows to Display",
      "min": 5,
      "max": 50,
      "step": 1,
      "default": 7
    },
    {
      "type": "text",
      "default": "technology_comparison",
      "id": "metaobject_type",
      "label": "Metaobject Type",
      "info": "Enter the metaobject type for technology comparison data"
    },
    {
      "type": "text",
      "id": "feature_labels",
      "label": "Feature Labels",
      "default": "Number of Measurements Taken,Scan Time,Precision/Accuracy,3D Whole Body Scan,Research Dashboard,Radiation-Free,No Fasting Required,Safe for pregnancy pacemaker and all populations,Health Risk Assessment,Body Composition,Posture,Circumferences,Body Volumes,Body Surface Area,Regional Circumferences,Regional Volumes,Regional Surface Area",
      "info": "Enter the feature labels for comparison in a comma-separated format"
    },
    {
      "type": "text",
      "id": "feature_keys",
      "label": "Feature Keys",
      "default": "number_of_measurements_taken,scan_time,precision_accuracy,3D_whole_body_scan,research_dashboard,radiation_free,no_fasting_required,safe_for_all_populations,health_risk_assessment,body_composition,posture,circumferences,body_volumes,body_surface_area,regional_circumferences,regional_volumes,regional_surface_area",
      "info": "Enter the feature keys for comparison in a comma-separated format"
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Margin Top",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Margin Bottom",
      "min": 0,
      "max": 80,
      "step": 2,
      "default": 0
    },
    {
      "type": "select",
      "id": "desktop_section_padding_top_bottom",
      "label": "Set the vertical padding (Desktop)",
      "info": "Set the vertical padding for desktop devices",
      "default": "md:py-16",
      "options": [
        {
          "value": "md:py-0",
          "label": "None"
        },
        {
          "value": "md:py-4",
          "label": "PY-4"
        },
        {
          "value": "md:py-6",
          "label": "PY-6"
        },
        {
          "value": "md:py-8",
          "label": "PY-8"
        },
        {
          "value": "md:py-10",
          "label": "PY-10"
        },
        {
          "value": "md:py-12",
          "label": "PY-12"
        },
        {
          "value": "md:py-14",
          "label": "PY-14"
        },
        {
          "value": "md:py-16",
          "label": "PY-16"
        },
        {
          "value": "md:py-20",
          "label": "PY-20"
        },
        {
          "value": "md:py-24",
          "label": "PY-24"
        },
        {
          "value": "md:py-28",
          "label": "PY-28"
        }
      ]
    },
    {
      "type": "select",
      "id": "mobile_section_padding_top_bottom",
      "label": "Set the vertical padding (Mobile)",
      "info": "Set the vertical padding for mobile devices",
      "default": "py-9",
      "options": [
        {
          "value": "py-0",
          "label": "None"
        },
        {
          "value": "py-4",
          "label": "PY-4"
        },
        {
          "value": "py-6",
          "label": "PY-6"
        },
        {
          "value": "py-8",
          "label": "PY-8"
        },
        {
          "value": "py-9",
          "label": "PY-9"
        },
        {
          "value": "py-10",
          "label": "PY-10"
        },
        {
          "value": "py-12",
          "label": "PY-12"
        },
        {
          "value": "py-14",
          "label": "PY-14"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Comparison Table"
    }
  ]
}
{% endschema %}
