<div
  id="mobile-popup-cart-item"
  class="fixed top-0 left-0 z-40 h-full overflow-y-auto transition-transform -translate-x-full bg-white w-full"
  tabindex="-1"
>
  <div class="flex flex-col gap-2">
    <div class="flex justify-between p-4">
      <div class="cart-header">
        <h3 id="cart-popup-label" class="heading-level-4">
          {{ 'cart.title' | t }}
        </h3>
      </div>
      <button
        type="button"
        data-drawer-hide="mobile-popup-cart-item"
        aria-controls="mobile-popup-cart-item"
        class="bg-transparent hover:bg-gray-4 rounded-lg text-sm w-8 h-8 absolute top-2.5 end-2.5 inline-flex items-center justify-center"
      >
        {{- 'icon-cross.svg' | inline_asset_content -}}
      </button>
    </div>
    <form class="flex flex-col gap-4" method="post" action="{{ routes.cart_url }}">
      <div class="mobile-popup-cart-body">
        {% if cart.item_count == 0 %}
          <div class="empty-cart-block px-4">
            {% render 'empty-cart', title_classes: 'text-xl font-bold text-secondary mt-4 mb-2', border: true %}
          </div>
        {% else %}
          {% render 'customer-order-comparison-for-cart-items',
            cart_items: cart.items,
            title_classes: 'text-base font-bold text-secondary',
            list_wrapper_classes: 'overflow-y-auto custom-scrollbar mr-3 pt-0 pr-1.5 pb-5 pl-5',
            cart_popup: true,
            sidebar_item: true
          %}
        {% endif %}
      </div>
    </form>
  </div>
</div>
