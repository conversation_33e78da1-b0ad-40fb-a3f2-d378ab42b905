import { debounce } from '../debounce.js';
import { ON_CHANGE_DEBOUNCE_TIMER } from '../constants-locations.js';

/**
 * MapService handles all Google Maps related operations
 * Manages map initialization, markers, clustering, and map interactions
 */
export class MapService {
  constructor(mapOptions, googleMapId) {
    this.mapOptions = mapOptions;
    this.googleMapId = googleMapId;
    this.map = null;
    this.mapMarkers = [];
    this.markerClusterer = null;
    this.infoWindow = null;
    this.idleListener = null;
    this.debouncedIdleHandler = null;
  }

  /**
   * Initializes the Google Map instance within the given container.
   */
  async initializeMap(container, onMapChangeCallback) {
    const finalMapOptions = {
      ...this.mapOptions,
      mapId: this.googleMapId,
    };

    this.map = new google.maps.Map(container, finalMapOptions);

    // Create a debounced version of the idle handler
    this.debouncedIdleHandler = debounce(() => {
      const bounds = this.map.getBounds();
      if (bounds && onMapChangeCallback) {
        onMapChangeCallback(bounds);
      }
    }, ON_CHANGE_DEBOUNCE_TIMER);

    this.idleListener = this.map.addListener('idle', this.debouncedIdleHandler);

    return this.map;
  }

  /**
   * Centers the map on a given position and updates the zoom level.
   */
  centerMapUpdateZoomLevel(position, zoomLevel) {
    if (!this.map) return;
    this.map.setCenter(position);
    this.map.setZoom(zoomLevel);
  }

  /**
   * Creates a new Google Maps marker for the given location.
   */
  createMarker(location) {
    const mapMarker = document.createElement('div');
    const img = document.createElement('img');
    img.setAttribute('loading', 'lazy');
    img.setAttribute('alt', location.companyName);
    img.setAttribute('aria-label', location.companyName);
    img.setAttribute('class', 'marker-icon');
    img.src = location.isHealthPassPartner
      ? 'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/marker-primary-gradient.svg'
      : 'https://cdn.shopify.com/s/files/1/0674/2579/6323/files/secondary-marker.svg';
    mapMarker.appendChild(img);

    const marker = new google.maps.marker.AdvancedMarkerElement({
      position: { lat: parseFloat(location.latitude), lng: parseFloat(location.longitude) },
      map: this.map,
      zIndex: location.isHealthPassPartner ? 1 : 0,
      title: location.companyName,
      content: mapMarker,
    });

    // Add isSelected property to track selected state
    marker.isSelected = false;
    marker.locationId = location.userId; // Store location ID for tracking

    return marker;
  }

  /**
   * Displays the provided location markers on the map.
   * Optimized to only update changed markers instead of clearing all markers.
   */
  placeMarkers(locations, onMarkerClickCallback) {
    // Skip update if markers are already correct
    if (!this.markersNeedUpdate(locations)) {
      return this.mapMarkers;
    }

    // Get current map bounds to optimize marker visibility
    const bounds = this.map ? this.map.getBounds() : null;

    // Create a set of location IDs for quick lookup
    const newLocationIds = new Set(locations.map((loc) => loc.userId));

    // Remove markers that are no longer needed or outside bounds
    this.mapMarkers = this.mapMarkers.filter((marker) => {
      const shouldKeep = marker.locationId && newLocationIds.has(marker.locationId);
      const withinBounds = !bounds || bounds.contains(marker.position);

      if (!shouldKeep || !withinBounds) {
        marker.setMap(null);
        return false;
      }
      return true;
    });

    // Get existing marker location IDs
    const existingLocationIds = new Set(this.mapMarkers.map((marker) => marker.locationId));

    // Add new markers for locations that don't already have markers
    locations.forEach((location) => {
      if (!existingLocationIds.has(location.userId)) {
        const marker = this.createMarker(location);
        this.mapMarkers.push(marker);

        // Add a click listener to handle marker interactions
        if (onMarkerClickCallback) {
          marker.addListener('gmp-click', () => {
            onMarkerClickCallback(marker, location);
          });
        }
      }
    });

    return this.mapMarkers;
  }

  /**
   * Initializes or updates a MarkerClusterer with custom rendering and clustering logic.
   */
  initMarkerClusterer(markers) {
    // If clusterer already exists, update it with new markers instead of recreating
    if (this.markerClusterer) {
      this.markerClusterer.clearMarkers();
      this.markerClusterer.addMarkers(markers);
    } else {
      // Create new clusterer
      this.markerClusterer = new markerClusterer.MarkerClusterer({
        markers: markers,
        map: this.map,
        algorithm: new markerClusterer.GridAlgorithm({ gridSize: 38, maxZoom: 13 }),
        renderer: {
          render: ({ count, position, markers, map }) => {
            const clusterElement = document.createElement('div');
            clusterElement.className = 'custom-cluster';
            clusterElement.innerHTML = `<div class="cluster-content"><span class="cluster-count">${count}</span></div>`;
            clusterElement.addEventListener('click', () => {
              const bounds = new google.maps.LatLngBounds();
              markers.forEach((marker) => bounds.extend(marker.position));
              map.fitBounds(bounds, { padding: { top: 18, right: 18, bottom: 18, left: 18 } });
            });
            return new google.maps.marker.AdvancedMarkerElement({
              position,
              content: clusterElement,
            });
          },
        },
      });
    }
  }

  /**
   * Removes all existing markers from the map and clears the marker storage.
   */
  clearMarkers() {
    if (this.markerClusterer) {
      this.markerClusterer?.clearMarkers();
      this.markerClusterer = null;
    }

    this.mapMarkers.forEach((marker) => marker.setMap(null));
    this.mapMarkers = [];
  }

  /**
   * Optimized marker clearing that only removes markers outside current bounds.
   */
  clearMarkersOutsideBounds(bounds) {
    if (!bounds) return;

    this.mapMarkers = this.mapMarkers.filter((marker) => {
      const withinBounds = bounds.contains(marker.position);
      if (!withinBounds) {
        marker.setMap(null);
        return false;
      }
      return true;
    });
  }

  /**
   * Checks if the current markers match the provided locations.
   */
  markersNeedUpdate(locations) {
    // If different number of locations, definitely need update
    if (this.mapMarkers.length !== locations.length) {
      return true;
    }

    // Check if all location IDs match
    const currentLocationIds = new Set(this.mapMarkers.map((marker) => marker.locationId));
    const newLocationIds = new Set(locations.map((loc) => loc.userId));

    // If sets are different sizes or have different values, need update
    if (currentLocationIds.size !== newLocationIds.size) {
      return true;
    }

    for (let id of newLocationIds) {
      if (!currentLocationIds.has(id)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Opens an info window on the specified marker.
   */
  openInfoWindow(marker, location, options = {}) {
    if (!this.infoWindow) {
      this.infoWindow = new google.maps.InfoWindow();
    }

    // Create and set InfoWindow content
    const infoContent = `<div class="info-window-wrapepr"><h2 class="text-sm font-semibold text-secondary">${location.companyName}</h2></div>`;
    this.infoWindow.setContent(infoContent);

    // Default options, with support for override
    const infoWindowOptions = {
      pixelOffset: new google.maps.Size(0, -12),
      disableAutoPan: options.disableAutoPan ?? false,
    };

    this.infoWindow.setOptions(infoWindowOptions);

    // Open InfoWindow on the specified marker
    this.infoWindow.open({
      map: this.map,
      position: marker.position,
      anchor: marker,
    });

    marker.isSelected = true; // Mark this marker as selected
    marker.zIndex = 2; // Show the selected marker at top
    marker.content?.classList.add('selected-marker'); // Add custom styling
  }

  /**
   * Closes the info window and resets marker selection states.
   */
  closeInfoWindow() {
    if (this.infoWindow) {
      this.infoWindow.close();
    }

    // Reset marker selection state
    this.mapMarkers.forEach((marker) => {
      marker.isSelected = false;
      marker.zIndex = 0;
      marker.content?.classList.remove('selected-marker');
    });
  }

  /**
   * Gets the map bounds.
   */
  getBounds() {
    return this.map ? this.map.getBounds() : null;
  }

  /**
   * Gets the map center.
   */
  getCenter() {
    return this.map ? this.map.getCenter() : null;
  }

  /**
   * Gets the current zoom level.
   */
  getZoom() {
    return this.map ? this.map.getZoom() : null;
  }

  /**
   * Cleanup method to remove event listeners.
   */
  destroy() {
    if (this.idleListener) {
      google.maps.event.removeListener(this.idleListener);
      this.idleListener = null;
    }

    if (this.debouncedIdleHandler) {
      this.debouncedIdleHandler = null;
    }

    this.clearMarkers();
  }
}
