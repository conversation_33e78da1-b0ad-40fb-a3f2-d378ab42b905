// Refresh cart contents (Popup cart, count badges)
async function refreshCartContents() {
  try {
    const popupCartElement = document.querySelector('#cart-popup');
    const cartWrapperElement = document.querySelector('#cartWrapper');
    const cartItemMobilePopup = document.querySelector('#mobile-popup-cart-item');
    const sellingPlanProductButtons = getButtonsWithSellingPlanProduct(); // Get all buttons with selling plan product

    const response = await fetch(window.location.href); // Fetch the current page content
    if (!response.ok) {
      throw new Error(`Network response was not ok: ${response.statusText}`);
    }

    const responseData = await response.text(); // Get the response text
    const parsedDocument = new DOMParser().parseFromString(responseData, 'text/html'); // Parse the text into an HTML document

    // Select updated elements
    const newCartBodyElement = parsedDocument.querySelector('.cart-body');
    const newMobilePopupCartBodyElement = parsedDocument.querySelector('.mobile-popup-cart-body');
    const newCartItemWrapperElement = parsedDocument.querySelector('.cart-item-wrapper');
    const newCheckoutButtonBlockElement = parsedDocument.querySelector('.checkout-button-block');
    const newCartFooterElement = parsedDocument.querySelector('.cart-popup-footer');
    const newCartHeaderElement = parsedDocument.querySelector('.cart-header');
    const newCartPopupHeaderElement = parsedDocument.querySelector('.cart-popup-header-block');
    const newCartCountBadgeElement = parsedDocument.querySelector('.cart-count-badge');
    const newProductQuantity = parsedDocument.querySelector('.product-quantity');

    if (sellingPlanProductButtons.length > 0) {
      const newProductButtons = getButtonsWithSellingPlanProduct(parsedDocument);
      sellingPlanProductButtons.forEach((oldButton, index) => {
        const newButton = newProductButtons[index];
        if (oldButton && newButton) {
          oldButton.replaceWith(newButton);
        }
      });
    }

    popupCartElement?.querySelector('.cart-body')?.replaceWith(newCartBodyElement);
    popupCartElement?.querySelector('.cart-popup-footer')?.replaceWith(newCartFooterElement);
    popupCartElement
      ?.querySelector('.cart-popup-header-block')
      ?.replaceWith(newCartPopupHeaderElement || document.createElement('div'));

    // Replace product quantity (outside cart wrapper)
    document.querySelector('.product-quantity')?.replaceWith(newProductQuantity);

    // Replace mobile popup cart body
    if (newMobilePopupCartBodyElement) {
      cartItemMobilePopup?.querySelector('.mobile-popup-cart-body')?.replaceWith(newMobilePopupCartBodyElement);

      // Apply height after DOM paint
      requestAnimationFrame(() => {
        const updatedResponsiveCartItemBlock = cartItemMobilePopup?.querySelector('.responsive-cart-item-block');
        if (updatedResponsiveCartItemBlock && window.matchMedia('(max-width: 768px)').matches) {
          updatedResponsiveCartItemBlock.style.height = `${window.innerHeight - 68}px`;
          updatedResponsiveCartItemBlock.style.overflowY = 'auto';
          updatedResponsiveCartItemBlock.style.position = 'fixed';
          updatedResponsiveCartItemBlock.style.width = '100%';
          updatedResponsiveCartItemBlock.style.paddingBottom = 'env(safe-area-inset-bottom)';
        }
      });
    }

    // Replace cart content blocks in desktop/cartWrapper
    cartWrapperElement?.querySelector('.cart-item-wrapper')?.replaceWith(newCartItemWrapperElement);
    if (newCheckoutButtonBlockElement) {
      cartWrapperElement?.querySelector('.checkout-button-block')?.replaceWith(newCheckoutButtonBlockElement);
    } else {
      cartWrapperElement?.querySelector('.cart-item-form-block')?.remove();
    }
    cartWrapperElement?.querySelector('.cart-header')?.replaceWith(newCartHeaderElement);

    // Update all cart count badges
    if (newCartCountBadgeElement) {
      document.querySelectorAll('.cart-count-badge').forEach((badgeElement) => {
        badgeElement.textContent = newCartCountBadgeElement.textContent;
        badgeElement.removeAttribute('hidden');
      });
    }

    // Reattach cart interaction handlers
    attachChangeCartQuantityEventListeners();
  } catch (error) {
    console.error('Failed to refresh cart contents:', error);
  } finally {
    disableButtons('.checkout', false);
  }
}

function getButtonsWithSellingPlanProduct(target = document) {
  return Array.from(target.querySelectorAll('.btn-atc')).filter((btn) => btn.dataset.sellingPlanProduct === 'true');
}

window.onSubmitAddToCartForm = async (form, event) => {
  event.preventDefault();

  const addToCartButton = event.target.querySelector('.btn-atc');
  const spinnerIcon = event.target.querySelector('.animate-spin');
  const quantityElement = document.querySelector('.product-quantity-input');
  const quantity = quantityElement ? quantityElement.value : 1;

  const productTypeFromElement = event.target.querySelector('input[name="id"]').dataset.productType;
  if (productTypeFromElement === PRODUCT_TYPE.HEALTH_PASS && addToCartButton.dataset.sellingPlanInCart) {
    addToCartButton.disabled = true;
    return;
  }

  addToCartButton.disabled = true;
  spinnerIcon.classList.remove('hidden');

  try {
    const formData = new FormData(form);
    const requestBody = Object.fromEntries(formData.entries());

    const response = await addItemToCart(requestBody, quantity);
    if (response.ok) {
      deleteLocalStorageItem(LOCAL_STORAGE_KEY.LOCATION_ID);
      deleteLocalStorageItem(LOCAL_STORAGE_KEY.REFERRAL_DATA);
    }

    await refreshCartContents(response);

    handleCartPopupDisplay();
    redirectToHealthProductPage(response);
  } catch (error) {
    console.error('Error while adding item to cart:', error.message);
  } finally {
    spinnerIcon.classList.add('hidden');
    addToCartButton.disabled = false;
  }
};

function handleCartPopupDisplay() {
  if (window.matchMedia('(max-width: 768px)').matches) {
    showMobileCartNotification();
  } else {
    document.getElementById('cart-popup').classList.remove('invisible', 'opacity-0');
  }
}

function redirectToHealthProductPage(response) {
  if (response.ok && window.location.pathname === PAGE_URLS.PRICING) {
    window.location.href = PAGE_URLS.HEALTH_PRODUCT;
  }
}

async function updateCartItems(updates, lineItemWrapper) {
  try {
    disableButtons('.checkout', true);
    createLoadingSpinner(lineItemWrapper, true);

    const updateCartItemPayload = API_PAYLOADS.UPDATE_CART_ITEM;
    updateCartItemPayload.body = JSON.stringify({ updates });

    const response = await fetchData(updateCartItemPayload);

    if (response.ok) {
      refreshCartContents(response);
    }
  } catch (error) {
    console.error('Error while updating cart item quantity:', error);
    throw new Error('Error while updating cart item quantity.');
  } finally {
    createLoadingSpinner(lineItemWrapper, false);
  }
}

async function addItemToCart(requestBody, quantity) {
  const addCartToItemPayload = API_PAYLOADS.ADD_ITEM_TO_CART;

  const referralData = new ReferralTracking();
  const trackingData = referralData.getTrackingDataFromLocal();
  const trakdesk_cid = referralData.getTrackingDataFromCookie('trakdesk_cid') || '';

  const parsedTrakdeskCid = JSON.parse(trakdesk_cid || '{}');
  const cidValue = parsedTrakdeskCid.cid;

  const locationId = getLocalStorageItem(LOCAL_STORAGE_KEY.LOCATION_ID) || trackingData.locationId || '';
  const referralTimestamp = locationId || trackingData ? new Date().toISOString() : '';

  const cartAttributes = {
    referralTimestamp,
    linkId: trackingData.linkId || '',
    sourceId: trackingData.sourceId || '',
    tenantId: trackingData.tenantId || '',
    trakdesk_cid: parsedTrakdeskCid,
    cid: cidValue,
    location_id: locationId,
  };

  addCartToItemPayload.body = JSON.stringify({
    attributes: cartAttributes,
    items: [
      {
        id: requestBody.id,
        selling_plan: requestBody.selling_plan,
        quantity: quantity,
      },
    ],
  });

  return await fetchData(addCartToItemPayload);
}

// Handler for changing quantity inputs
function attachChangeCartQuantityEventListeners() {
  document.querySelectorAll('.increment-button, .decrement-button').forEach((button) => {
    button.addEventListener('click', async (event) => {
      updateCartQuantity(
        event.currentTarget.closest('.line-item'),
        event.currentTarget.classList.contains('increment-button') ? 1 : -1
      );
    });
  });

  document.querySelectorAll('.quantity-input').forEach((input) => {
    input.addEventListener('change', () => onChangeCartQty(input));
  });

  document.querySelector('.quantity-increment-button')?.addEventListener('click', () => adjustProductQuantity(1));
  document.querySelector('.quantity-decrement-button')?.addEventListener('click', () => adjustProductQuantity(-1));
}

// Attach CartQuantity EventListeners initially to set up the listeners
attachChangeCartQuantityEventListeners();

window.onChangeCartQty = async (input) => {
  const lineItem = input.closest('.line-item');

  const variantId = lineItem.dataset.productVariantId;
  const lineItemKey = lineItem.dataset.lineItemKey;
  let quantity = parseInt(input.value, 10);

  try {
    disableButtons('.checkout', true);
    createLoadingSpinner(lineItem, true);

    await updateCartItems({ [lineItemKey]: quantity }, lineItem);
  } catch (error) {
    console.error(`Error while changing cart quantity for variant ${variantId}:`, error);
  } finally {
    createLoadingSpinner(lineItem, false);
    disableButtons('.checkout', false);
  }
};

async function updateCartQuantity(lineItemWrapper, quantityChange) {
  try {
    disableButtons('.checkout', true);
    createLoadingSpinner(lineItemWrapper, true);

    const lineItemKey = lineItemWrapper.dataset.lineItemKey;

    const input = lineItemWrapper.querySelector(`[id="quantity-input_${lineItemKey}"]`);
    let quantity = parseInt(input.value) + quantityChange;

    if (quantity < input.dataset.inputCounterMin) {
      quantity = input.dataset.inputCounterMin;
    } else if (quantity > input.dataset.inputCounterMax) {
      quantity = input.dataset.inputCounterMax;
    }

    await changeCartItemQuantity(lineItemKey, quantity);

    input.value = quantity;
  } catch (error) {
    console.error('Error while changing cart quantity:', error);
  } finally {
    createLoadingSpinner(lineItemWrapper, false);
  }
}

async function adjustProductQuantity(quantityChange) {
  const input = document.getElementById('productQuantityInput');

  const minQuantity = parseInt(input.dataset.inputCounterMin) || 1;
  let currentQuantity = parseInt(input.value) || 0;

  // Calculate new quantity
  let updatedQuantity = currentQuantity + quantityChange;

  if (updatedQuantity < minQuantity) {
    updatedQuantity = minQuantity;
  }

  if (updatedQuantity > 1) {
    input.previousElementSibling.disabled = false;
  } else {
    input.previousElementSibling.disabled = true;
  }

  input.value = updatedQuantity;
}

// Handler for removing items from cart
window.onRemoveCartItem = async (input) => {
  const lineItem = input.closest('.line-item');
  try {
    disableButtons('.checkout', true);
    createLoadingSpinner(lineItem, true);

    await changeCartItemQuantity(lineItem.dataset.lineItemKey, 0);
  } catch (error) {
    console.error('Error while removing cart item:', error);
  } finally {
    createLoadingSpinner(lineItem, false);
    disableButtons('.checkout', false);
  }
};

// Reusable function for changing cart item quantity
const changeCartItemQuantity = async (lineItemKey, quantity) => {
  try {
    const changeCartItemPayload = API_PAYLOADS.CHANGE_CART_ITEM;

    changeCartItemPayload.body = JSON.stringify({
      id: lineItemKey,
      quantity: quantity,
    });

    const response = await fetchData(changeCartItemPayload);

    if (response) {
      refreshCartContents(response);
    }
  } catch (error) {
    console.error('Error while changing cart item quantity:', error);
    throw new Error('Error while changing cart item quantity.');
  }
};

function createLoadingSpinner(targetedWrapper, visible) {
  if (visible) {
    targetedWrapper.children[0].classList.add('opacity-20');

    const loaderElement = document.createElement('div');
    loaderElement.setAttribute('id', 'customLoader');
    loaderElement.setAttribute('role', 'status');
    loaderElement.classList.add('absolute', '-translate-x-1/2', '-translate-y-1/2', 'top-2/4', 'left-1/2');

    const svg = `<svg aria-hidden="true" class="w-8 h-8 text-gray-2 animate-spin fill-[#D22725]" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/></svg>
        <span class="sr-only">Loading...</span>`;
    loaderElement.innerHTML = svg;

    targetedWrapper.appendChild(loaderElement);
  }
}

function disableButtons(targetedElement, status) {
  const buttonElements = document.querySelectorAll(targetedElement);
  buttonElements?.forEach((button) => {
    button.disabled = status;
  });
}

function showMobileCartNotification() {
  const notificationElement = document.getElementById('cart-notification');
  notificationElement.classList.remove('hidden');

  setTimeout(() => {
    notificationElement.classList.add('hidden');
  }, 5000);
}
