document.addEventListener('DOMContentLoaded', () => {
  const header = document.querySelector('#shopify-section-header header');
  const openMobileNavigation = document.querySelector('#openMobileNavigation');
  const closeMobileNavigation = document.querySelector('#closeMobileNavigation');
  const navbarCollapse = document.querySelector('#navbarCollapse');
  const menuDrawerNavigationContainer = document.querySelector('.menu-drawer-navigation-container');
  const popupCartItemEl = document.querySelector('#mobile-popup-cart-item');
  const popupCartTargetEl = document.querySelector('[data-drawer-target="mobile-popup-cart-item"]');
  const popupCartHideBtnEl = document.querySelector('[data-drawer-hide="mobile-popup-cart-item"]');
  const cartItemBlockEl = popupCartItemEl?.querySelector('.responsive-cart-item-block');
  const profileDropdown = document.getElementById('profileDropdown');
  const logoutLinks = document.querySelectorAll('a[href^="/account/logout"]');
  const accountLinkBlocks = document.querySelectorAll('.header-profile-link-block .account-links');

  // Sticky header on scroll
  window.onscroll = () => {
    if (!header) return;
    const stickyThreshold = header.offsetTop;
    header.classList.toggle('sticky-header', window.pageYOffset > stickyThreshold);
  };

  // Disable automatic scroll restoration
  if ('scrollRestoration' in history) {
    history.scrollRestoration = 'manual';
  }

  // Utility: Calculate adjusted height
  const adjustHeight = (reduceHeight = 0) => `${window.innerHeight - reduceHeight}px`;

  // Update mobile menu + drawer heights
  function updateResponsiveHeights() {
    const isMobile = window.matchMedia('(max-width: 768px)').matches;
    if (!navbarCollapse || !menuDrawerNavigationContainer) return;

    if (isMobile) {
      navbarCollapse.style.height = adjustHeight();
      menuDrawerNavigationContainer.style.height = adjustHeight(88);
    } else {
      [navbarCollapse, menuDrawerNavigationContainer].forEach((el) => {
        if (el) {
          el.style.height = '';
          el.style.overflowY = '';
        }
      });
    }
  }

  // Set cart popup height dynamically (mobile only)
  function updateCartPopupHeight() {
    const dynamicCartItemBlock = document.querySelector('#mobile-popup-cart-item .responsive-cart-item-block');
    if (dynamicCartItemBlock && window.matchMedia('(max-width: 768px)').matches) {
      dynamicCartItemBlock.style.height = `${window.innerHeight - 68}px`;
      dynamicCartItemBlock.style.overflowY = 'auto';
      dynamicCartItemBlock.style.position = 'fixed';
      dynamicCartItemBlock.style.width = '100%';
      dynamicCartItemBlock.style.paddingBottom = 'env(safe-area-inset-bottom)';
    }
  }

  // Reset cart popup styles
  function resetCartPopupHeight() {
    if (cartItemBlockEl) cartItemBlockEl.removeAttribute('style');
  }

  // Lock/unlock body scroll
  function toggleBodyScroll(lock = true) {
    const styles = lock
      ? { overflowY: 'hidden', position: 'fixed', width: '100%' }
      : { overflowY: '', position: '', width: '' };

    Object.assign(document.body.style, styles);
  }

  // Mobile Navigation Open
  openMobileNavigation?.addEventListener('click', () => {
    navbarCollapse?.classList.add('block');
    navbarCollapse?.classList.remove('hidden');
    navbarCollapse.style.height = adjustHeight();
    menuDrawerNavigationContainer.style.height = adjustHeight(88);
    closeMobileNavigation.classList.add('navbarTogglerActive');
    toggleBodyScroll(true);
  });

  // Mobile Navigation Close
  closeMobileNavigation?.addEventListener('click', () => {
    navbarCollapse?.classList.remove('block');
    navbarCollapse?.classList.add('hidden');
    navbarCollapse.style.height = '';
    menuDrawerNavigationContainer.style.height = '';
    toggleBodyScroll(false);
    closeMobileNavigation.classList.remove('navbarTogglerActive');
  });

  // Submenu toggle (mobile)
  if (window.matchMedia('(max-width: 768px)').matches) {
    document.querySelectorAll('.submenu-item').forEach((el) => {
      const anchor = el.querySelector('a');
      anchor?.addEventListener('click', (event) => {
        event.preventDefault();
        el.classList.toggle('submenu-item-active');
        anchor.classList.toggle('active');
        anchor.querySelector('.caret-icon svg')?.classList.toggle('active');
        el.querySelector('.submenu')?.classList.toggle('hidden');
      });
    });
  }

  // Popup cart open
  popupCartTargetEl?.addEventListener('click', () => {
    updateCartPopupHeight();
    toggleBodyScroll(true);
  });

  // Popup cart close
  popupCartHideBtnEl?.addEventListener('click', () => {
    resetCartPopupHeight();
    toggleBodyScroll(false);
  });

  // Account link behavior
  accountLinkBlocks?.forEach((link) => {
    link.addEventListener('click', function (event) {
      event.preventDefault();
      const href = this.getAttribute('href');
      if (!href) return;

      const redirectUrl = `${location.origin}${PAGE_URLS.ACCOUNT}${href}`;
      if (location.pathname === PAGE_URLS.ACCOUNT) {
        location.reload();
      } else {
        location.href = redirectUrl;
      }

      accountLinkBlocks.forEach((item) => item.classList.remove('active-link'));
      this.classList.add('active-link');
    });

    const href = link.getAttribute('href');
    if (href === window.location.hash) {
      link.classList.add('active-link');
    }
  });

  // Hide profile dropdown on item click
  profileDropdown?.querySelectorAll('.profile-dropdown-item').forEach((item) => {
    item.addEventListener('click', () => {
      profileDropdown.classList.add('hidden');
    });
  });

  // Logout link behavior
  logoutLinks.forEach((link) => {
    link.addEventListener('click', logoutHandler);
  });

  async function logoutHandler(event) {
    event.preventDefault();
    const exceptionMessage = window.errorMessage;

    try {
      toggleFullScreenPreloader(true);
      const accessToken = getLocalStorageItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
      const stykuRes = await fetchData(API_PAYLOADS.SIGN_OUT(accessToken));
      validateResponse(stykuRes, exceptionMessage.somethingWentWrong);

      const shopifyRes = await fetchData(API_PAYLOADS.SHOPIFY_CUSTOMER_SIGN_OUT);
      validateResponse(shopifyRes, exceptionMessage.somethingWentWrong);

      deleteLocalStorageItem(LOCAL_STORAGE_KEY.LOGGED_IN_USER);
      deleteLocalStorageItem(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
      window.location.replace(PAGE_URLS.SHOPIFY_LOGIN);
    } catch (err) {
      showToastMessage(exceptionMessage.somethingWentWrong);
      console.error('Logout Error:', err);
    } finally {
      toggleFullScreenPreloader(false);
    }
  }

  function validateResponse(response, errorMessage) {
    if (response.status !== SUCCESS_CODE.SUCCESS) {
      throw new Error(errorMessage);
    }
  }

  // Initial responsive height setup
  updateResponsiveHeights();
  updateCartPopupHeight();

  // On window resize
  window.addEventListener('resize', () => {
    updateResponsiveHeights();
    updateCartPopupHeight();
  });

  // Optional: handle orientation change
  window.addEventListener('resize', updateCartPopupHeight);
  window.addEventListener('orientationchange', updateCartPopupHeight);
});
