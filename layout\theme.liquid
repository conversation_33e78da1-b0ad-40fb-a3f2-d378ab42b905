<!doctype html>
<html class="no-js" lang="{{ request.locale.iso_code }}">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="">
    <link rel="canonical" href="{{ canonical_url }}">
    {%- if settings.favicon != blank -%}
      <link rel="icon" type="image/png" href="{{ settings.favicon | image_url: width: 32, height: 32 }}">
    {%- endif -%}
    {%- unless settings.type_header_font.system? and settings.type_body_font.system? -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endunless -%}
    <title>
      {{ page_title }}
      {%- if current_tags %} &ndash; tagged "{{ current_tags | join: ', ' }}"{% endif -%}
      {%- if current_page != 1 %} &ndash; Page {{ current_page }}{% endif -%}
      {%- unless page_title contains shop.name %} &ndash; {{ shop.name }}{% endunless -%}
    </title>
    {% if page_description %}
      <meta name="description" content="{{ page_description | escape }}">
    {% endif %}
    <meta name="robots" content="noindex, nofollow">
    {% render 'meta-tags' %}
    {% render 'force-home-redirect' %}
    {% render 'customer-login-redirect' %}
    {{ content_for_header }}
    {% render 'scripts-and-styles' %}
  </head>
  <body>
    {% section 'header' %}
    <main role="main">
      {{ content_for_layout }}
    </main>
    {% section 'footer' %}
    {% render 'cart-popup' %}
    {% render 'mobile-popup-cart-item' %}
    {% render 'toast-notification' %}
    <script>
      function handleImageLoad(img) {
        img.classList.add('loaded');
      }

      // For regular and lazy-loaded images
      document.querySelectorAll('img.blur-loading').forEach((img) => {
        if (img.complete && img.naturalWidth !== 0) {
          handleImageLoad(img);
        } else {
          img.addEventListener('load', () => handleImageLoad(img));
        }
      });
    </script>
    {% render 'trackdesk-tracker' %}
    <script src="{{ 'flowbite.min.js' | asset_url }}" defer></script>
    {% render 'localization-string-resources' %}
    {% render 'owl-carousel-initialization' %}
  </body>
</html>
