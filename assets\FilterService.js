import { URL_PARAMS } from '../constants-locations.js';

/**
 * FilterService handles all filter logic, state management, and URL parameter updates
 * No direct DOM manipulation or Google Maps interaction
 */
export class FilterService {
  constructor(dataset, defaultValues) {
    this.dataset = dataset;
    this.defaultValues = defaultValues;
    this.isMobile = window.matchMedia('(max-width: 768px)').matches;
    this.isEnabledQueryParams = dataset.enableUpdateQuearyParams === 'true';
    
    // Filter state
    this.selectedNearByMe = null;
    this.selectedInputSearch = null;
    this.selectedRadius = defaultValues.radius;
    this.selectedBusinessType = defaultValues.businessType;
    this.selectedLocationCoordinates = defaultValues.coordinates;
    this.selectedFilterCount = 0;
    
    // Filtering behavior
    this.hasApplyButton = false;
    this.shouldUseDelayedMobileFiltering = false;
    
    this.detectFilteringBehavior();
  }

  /**
   * Detects if Apply Now button exists and sets filtering behavior accordingly.
   */
  detectFilteringBehavior() {
    this.hasApplyButton = !!document.getElementById('applyAllFilters');
    this.shouldUseDelayedMobileFiltering = this.isMobile && this.hasApplyButton;
  }

  /**
   * Checks if we should use delayed mobile filtering.
   */
  shouldUseDelayedFiltering() {
    if (this.shouldUseDelayedMobileFiltering === null) {
      return false;
    }
    return this.shouldUseDelayedMobileFiltering;
  }

  /**
   * Checks if there are any active filters applied.
   */
  hasActiveFilters() {
    return (
      this.selectedNearByMe !== null ||
      this.selectedInputSearch !== null ||
      this.selectedRadius !== this.defaultValues.radius ||
      this.selectedBusinessType !== this.defaultValues.businessType
    );
  }

  /**
   * Handles radius selection logic.
   */
  handleRadiusSelection(selectedRadius) {
    this.selectedRadius = selectedRadius;
    
    // Return whether to apply immediately
    return !this.shouldUseDelayedFiltering();
  }

  /**
   * Handles business type filter selection logic.
   */
  handleBusinessTypeFilterSelection(selectedBusinessType) {
    this.selectedBusinessType = selectedBusinessType === 'all_facilities' 
      ? this.defaultValues.businessType 
      : selectedBusinessType;
    
    // Return whether to apply immediately
    return !this.shouldUseDelayedFiltering();
  }

  /**
   * Updates URL query parameters based on filter values.
   */
  updateURLQueryParams(nearByMe, search, lat, lng, radius, businessType, isApplyFilters = false) {
    if (this.isMobile && !isApplyFilters) return;

    const params = new URLSearchParams(window.location.search);

    // Helper function to set or delete parameter
    const setOrDeleteParam = (key, value) => {
      if (value != null && value !== '') {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    };

    setOrDeleteParam(URL_PARAMS.NEAR_BY_ME, nearByMe);
    setOrDeleteParam(URL_PARAMS.SEARCH, search);
    setOrDeleteParam(URL_PARAMS.LAT, lat);
    setOrDeleteParam(URL_PARAMS.LNG, lng);
    setOrDeleteParam(URL_PARAMS.RADIUS, radius);
    setOrDeleteParam(URL_PARAMS.BUSINESS_TYPE, businessType);

    window.history.replaceState({}, '', `${window.location.pathname}?${params}`);
  }

  /**
   * Parses URL search parameters and updates filter state.
   */
  parseSearchParams() {
    const params = new URLSearchParams(window.location.search);

    this.selectedNearByMe = params.get(URL_PARAMS.NEAR_BY_ME) === 'true' || null;
    this.selectedInputSearch = params.get(URL_PARAMS.SEARCH);

    const latitude = params.get(URL_PARAMS.LAT);
    const longitude = params.get(URL_PARAMS.LNG);
    if (latitude && longitude) {
      this.selectedLocationCoordinates = { 
        lat: parseFloat(latitude), 
        lng: parseFloat(longitude) 
      };
    } else {
      this.selectedLocationCoordinates = this.defaultValues.coordinates;
    }

    const radiusString = params.get(URL_PARAMS.RADIUS);
    if (radiusString) {
      this.selectedRadius = parseInt(radiusString);
    } else {
      this.selectedRadius = this.defaultValues.radius;
    }

    this.selectedBusinessType = params.get(URL_PARAMS.BUSINESS_TYPE);

    return {
      nearByMe: this.selectedNearByMe,
      search: this.selectedInputSearch,
      coordinates: this.selectedLocationCoordinates,
      radius: this.selectedRadius,
      businessType: this.selectedBusinessType
    };
  }

  /**
   * Resets all filter values to defaults.
   */
  resetAllFilters() {
    this.selectedNearByMe = null;
    this.selectedBusinessType = this.defaultValues.businessType;
    this.selectedRadius = this.defaultValues.radius;
    this.selectedInputSearch = null;
    this.selectedFilterCount = 0;
    this.selectedLocationCoordinates = this.defaultValues.coordinates;
  }

  /**
   * Updates filter state for Near Me functionality.
   */
  setNearMeState(isActive, coordinates = null) {
    this.selectedNearByMe = isActive;
    if (coordinates) {
      this.selectedLocationCoordinates = coordinates;
    }
    if (isActive) {
      this.selectedInputSearch = null;
    }
  }

  /**
   * Updates filter state for search input.
   */
  setSearchState(searchText, coordinates = null) {
    this.selectedInputSearch = searchText;
    if (coordinates) {
      this.selectedLocationCoordinates = coordinates;
    }
    if (searchText) {
      this.selectedNearByMe = null;
    }
  }

  /**
   * Gets current filter state.
   */
  getFilterState() {
    return {
      nearByMe: this.selectedNearByMe,
      search: this.selectedInputSearch,
      radius: this.selectedRadius,
      businessType: this.selectedBusinessType,
      coordinates: this.selectedLocationCoordinates,
      filterCount: this.selectedFilterCount
    };
  }

  /**
   * Sets the location coordinates.
   */
  setLocationCoordinates(coordinates) {
    this.selectedLocationCoordinates = coordinates;
  }

  /**
   * Gets whether query parameters are enabled.
   */
  isQueryParamsEnabled() {
    return this.isEnabledQueryParams;
  }

  /**
   * Counts active filters for UI display.
   */
  countActiveFilters() {
    let count = 0;
    
    if (this.selectedInputSearch) count++;
    if (this.selectedNearByMe) count++;
    if (this.selectedBusinessType && this.selectedBusinessType !== this.defaultValues.businessType) count++;
    if (this.selectedRadius && this.selectedRadius !== this.defaultValues.radius) count++;
    
    this.selectedFilterCount = count;
    return count;
  }
}
